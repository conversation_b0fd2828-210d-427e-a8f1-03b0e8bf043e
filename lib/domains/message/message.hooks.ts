"use client"

import { useCallback } from "react"
import { useMessageStore, useMessageSelectors } from "./message.store"
import { MessageCreateData } from "./message.types"

// Export real-time hooks
export * from "./message.realtime.hooks"

/**
 * Hook for sending messages
 * @param tripId Trip ID
 * @returns Send message function and sending state
 */
export function useSendMessage(tripId: string) {
  const sendMessage = useMessageStore((state) => state.sendMessage)
  const sending = useMessageSelectors.sending(tripId)
  const error = useMessageSelectors.error(tripId)

  const handleSendMessage = useCallback(
    async (messageData: MessageCreateData) => {
      await sendMessage(tripId, messageData)
    },
    [sendMessage, tripId]
  )

  return {
    sendMessage: handleSendMessage,
    sending,
    error,
  }
}

/**
 * Hook for loading more messages (infinite scroll)
 * @param tripId Trip ID
 * @returns Load more function and loading state
 */
export function useLoadMoreMessages(tripId: string) {
  const loadMoreMessages = useMessageStore((state) => state.loadMoreMessages)
  const loading = useMessageSelectors.loading(tripId)
  const hasMore = useMessageSelectors.hasMore(tripId)
  const error = useMessageSelectors.error(tripId)

  const handleLoadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await loadMoreMessages(tripId)
    }
  }, [loadMoreMessages, tripId, loading, hasMore])

  return {
    loadMore: handleLoadMore,
    loading,
    hasMore,
    error,
  }
}

/**
 * Hook for managing new message notifications
 * @param tripId Trip ID
 * @returns New message count and reset function
 */
export function useNewMessageNotifications(tripId: string) {
  const newMessageCount = useMessageSelectors.newMessageCount(tripId)
  const resetNewMessageCount = useMessageStore((state) => state.resetNewMessageCount)

  const resetCount = useCallback(() => {
    resetNewMessageCount(tripId)
  }, [resetNewMessageCount, tripId])

  return {
    newMessageCount,
    resetCount,
  }
}

/**
 * Hook for getting trip messages
 * @param tripId Trip ID
 * @returns Messages and loading state
 */
export function useMessages(tripId: string) {
  const messages = useMessageSelectors.messages(tripId)
  const loading = useMessageSelectors.loading(tripId)
  const error = useMessageSelectors.error(tripId)

  return {
    messages,
    loading,
    error,
  }
}

/**
 * Hook for clearing trip messages when leaving a trip
 * @param tripId Trip ID
 * @returns Clear function
 */
export function useClearMessages(tripId: string) {
  const clearTripMessages = useMessageStore((state) => state.clearTripMessages)

  const clearMessages = useCallback(() => {
    clearTripMessages(tripId)
  }, [clearTripMessages, tripId])

  return { clearMessages }
}
