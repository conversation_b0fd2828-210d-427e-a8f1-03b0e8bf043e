"use client"

import { useCallback } from "react"
import {
  useMessageStore,
  useMessages as useMessagesSelector,
  useMessageLoading,
  useMessageError,
  useMessageSending,
  useMessageHasMore,
  useNewMessageCount as useNewMessageCountSelector,
} from "./message.store"
import { MessageCreateData } from "./message.types"

// Export real-time hooks
export * from "./message.realtime.hooks"

/**
 * Hook for sending messages
 * @param tripId Trip ID
 * @returns Send message function and sending state
 */
export function useSendMessage(tripId: string) {
  const sendMessage = useMessageStore((state) => state.sendMessage)
  const sending = useMessageSending(tripId)
  const error = useMessageError(tripId)

  const handleSendMessage = useCallback(
    async (messageData: MessageCreateData) => {
      await sendMessage(tripId, messageData)
    },
    [sendMessage, tripId]
  )

  return {
    sendMessage: handleSendMessage,
    sending,
    error,
  }
}

/**
 * Hook for loading more messages (infinite scroll)
 * @param tripId Trip ID
 * @returns Load more function and loading state
 */
export function useLoadMoreMessages(tripId: string) {
  const loadMoreMessages = useMessageStore((state) => state.loadMoreMessages)
  const loading = useMessageLoading(tripId)
  const hasMore = useMessageHasMore(tripId)
  const error = useMessageError(tripId)

  const handleLoadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await loadMoreMessages(tripId)
    }
  }, [loadMoreMessages, tripId, loading, hasMore])

  return {
    loadMore: handleLoadMore,
    loading,
    hasMore,
    error,
  }
}

/**
 * Hook for managing new message notifications
 * @param tripId Trip ID
 * @returns New message count and reset function
 */
export function useNewMessageNotifications(tripId: string) {
  const newMessageCount = useNewMessageCountSelector(tripId)
  const resetNewMessageCount = useMessageStore((state) => state.resetNewMessageCount)

  const resetCount = useCallback(() => {
    resetNewMessageCount(tripId)
  }, [resetNewMessageCount, tripId])

  return {
    newMessageCount,
    resetCount,
  }
}

/**
 * Hook for getting trip messages
 * @param tripId Trip ID
 * @returns Messages and loading state
 */
export function useMessages(tripId: string) {
  const messages = useMessagesSelector(tripId)
  const loading = useMessageLoading(tripId)
  const error = useMessageError(tripId)

  return {
    messages,
    loading,
    error,
  }
}

/**
 * Hook for clearing trip messages when leaving a trip
 * @param tripId Trip ID
 * @returns Clear function
 */
export function useClearMessages(tripId: string) {
  const clearTripMessages = useMessageStore((state) => state.clearTripMessages)

  const clearMessages = useCallback(() => {
    clearTripMessages(tripId)
  }, [clearTripMessages, tripId])

  return { clearMessages }
}
