"use client"

import { useEffect, useRef } from "react"
import { useRealtimeCollection } from "../base/base.realtime.hooks"
import { MessageRealtimeService } from "./message.realtime.service"
import { useMessageStore } from "./message.store"
import { Message } from "./message.types"

/**
 * Hook for real-time trip messages subscription
 * @param tripId Trip ID
 * @param enabled Whether the subscription is enabled
 * @returns Messages, loading state, and error
 */
export function useRealtimeMessages(tripId: string, enabled: boolean = true) {
  const setMessages = useMessageStore((state) => state.setMessages)
  const setLoading = useMessageStore((state) => state.setLoading)
  const setError = useMessageStore((state) => state.setError)
  const setHasMore = useMessageStore((state) => state.setHasMore)
  const setLastDoc = useMessageStore((state) => state.setLastDoc)

  useEffect(() => {
    if (!enabled || !tripId) return

    setLoading(tripId, true)
    setError(tripId, null)

    const unsubscribe = MessageRealtimeService.subscribeToMessages(
      tripId,
      20, // Initial load of 20 messages
      undefined, // No pagination for initial load
      ({ messages, lastDoc }, error) => {
        if (error) {
          setError(tripId, error)
          setLoading(tripId, false)
          return
        }

        // Reverse messages to show newest at bottom
        const reversedMessages = [...messages].reverse()
        setMessages(tripId, reversedMessages)
        setLastDoc(tripId, lastDoc)
        setHasMore(tripId, messages.length === 20)
        setLoading(tripId, false)
      }
    )

    return unsubscribe
  }, [tripId, enabled, setMessages, setLoading, setError, setHasMore, setLastDoc])

  const messages = useMessageStore((state) => state.messages[tripId] || [])
  const loading = useMessageStore((state) => state.loading[tripId] || false)
  const error = useMessageStore((state) => state.error[tripId] || null)

  return { messages, loading, error }
}

/**
 * Hook for real-time new message notifications
 * @param tripId Trip ID
 * @param currentUserId Current user ID to exclude own messages
 * @param enabled Whether the subscription is enabled
 * @returns New message notifications
 */
export function useRealtimeNewMessages(
  tripId: string,
  currentUserId: string,
  enabled: boolean = true
) {
  const incrementNewMessageCount = useMessageStore((state) => state.incrementNewMessageCount)
  const lastMessageIdRef = useRef<string | null>(null)

  useEffect(() => {
    if (!enabled || !tripId || !currentUserId) return

    const unsubscribe = MessageRealtimeService.subscribeToLatestMessages(
      tripId,
      (messages, error) => {
        if (error || messages.length === 0) return

        const latestMessage = messages[0]

        // Skip if it's the user's own message or if we've already seen this message
        if (
          latestMessage.senderId === currentUserId ||
          latestMessage.id === lastMessageIdRef.current
        ) {
          return
        }

        // Update the last seen message ID
        lastMessageIdRef.current = latestMessage.id

        // Increment new message count
        incrementNewMessageCount(tripId)
      }
    )

    return unsubscribe
  }, [tripId, currentUserId, enabled, incrementNewMessageCount])
}

/**
 * Hook for managing message scroll behavior
 * @param tripId Trip ID
 * @param messagesContainerRef Ref to the messages container
 * @returns Scroll functions
 */
export function useMessageScroll(
  tripId: string,
  messagesContainerRef: React.RefObject<HTMLDivElement>
) {
  const messages = useMessageStore((state) => state.messages[tripId] || [])
  const resetNewMessageCount = useMessageStore((state) => state.resetNewMessageCount)

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight
      resetNewMessageCount(tripId)
    }
  }

  const scrollToTop = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = 0
    }
  }

  const isAtBottom = () => {
    if (!messagesContainerRef.current) return false
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current
    return scrollTop + clientHeight >= scrollHeight - 10 // 10px threshold
  }

  // Auto-scroll to bottom when new messages arrive (only if already at bottom)
  useEffect(() => {
    if (messages.length > 0 && isAtBottom()) {
      scrollToBottom()
    }
  }, [messages.length])

  return {
    scrollToBottom,
    scrollToTop,
    isAtBottom,
  }
}
