"use client"

import { useEffect } from "react"
import { NotificationRealtimeService } from "./notification.realtime.service"
import { useNotificationStore } from "./notification.store"

/**
 * Hook for real-time user notifications subscription
 * @param userId User ID
 * @param enabled Whether the subscription is enabled
 * @returns Notifications, loading state, and error
 */
export function useRealtimeNotifications(userId: string, enabled: boolean = true) {
  const setNotifications = useNotificationStore((state) => state.setNotifications)
  const setLoading = useNotificationStore((state) => state.setLoading)
  const setError = useNotificationStore((state) => state.setError)

  useEffect(() => {
    if (!enabled || !userId || userId.trim() === "") {
      setLoading(false)
      setNotifications([])
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = NotificationRealtimeService.subscribeToNotifications(
      userId,
      50, // Load up to 50 notifications
      (notifications, error) => {
        if (error) {
          console.error("Error in notification subscription:", error)
          setError(error)
          setLoading(false)
          return
        }

        setNotifications(notifications)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [userId, enabled, setNotifications, setLoading, setError])

  const notifications = useNotificationStore((state) => state.notifications)
  const loading = useNotificationStore((state) => state.loading)
  const error = useNotificationStore((state) => state.error)

  return { notifications, loading, error }
}

/**
 * Hook for real-time unread notification count subscription
 * @param userId User ID
 * @param enabled Whether the subscription is enabled
 * @returns Unread count and loading state
 */
export function useRealtimeUnreadCount(userId: string, enabled: boolean = true) {
  const setUnreadCount = useNotificationStore((state) => state.setUnreadCount)
  const setError = useNotificationStore((state) => state.setError)

  useEffect(() => {
    if (!enabled || !userId || userId.trim() === "") {
      setUnreadCount(0)
      return
    }

    setError(null)

    const unsubscribe = NotificationRealtimeService.subscribeToUnreadCount(
      userId,
      (count, error) => {
        if (error) {
          setError(error)
          return
        }

        setUnreadCount(count)
      }
    )

    return unsubscribe
  }, [userId, enabled, setUnreadCount, setError])

  const unreadCount = useNotificationStore((state) => state.unreadCount)

  return { unreadCount }
}
